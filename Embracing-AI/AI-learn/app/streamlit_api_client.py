"""
Streamlit API客户端
用于与后端API进行交互
"""

import aiohttp
import asyncio
import json
from typing import Dict, Any, Optional, List
import streamlit as st

class APIClient:
    """API客户端类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/system/health") as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def analyze_requirements(self, requirement_text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """需求分析"""
        payload = {
            "input_text": requirement_text,
            "context": context or {}
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/requirements/analyze",
                json=payload
            ) as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def review_requirements(self, requirements: List[Dict[str, Any]], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """需求评审"""
        payload = {
            "requirements": requirements,
            "context": context or {}
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/requirements/review",
                json=payload
            ) as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def generate_test_cases(self, requirements: List[Dict[str, Any]], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成测试用例"""
        payload = {
            "requirements": requirements,
            "context": context or {}
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/testcases/generate",
                json=payload
            ) as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def review_test_cases(self, test_cases: List[Dict[str, Any]], context: Dict[str, Any] = None) -> Dict[str, Any]:
        """评审测试用例"""
        payload = {
            "test_cases": test_cases,
            "context": context or {}
        }
        
        try:
            async with self.session.post(
                f"{self.base_url}/api/v1/testcases/review",
                json=payload
            ) as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def upload_document(self, file_content: bytes, filename: str, agent_type: str = "requirement_analysis") -> Dict[str, Any]:
        """上传文档"""
        try:
            data = aiohttp.FormData()
            data.add_field('file', file_content, filename=filename)
            data.add_field('agent_type', agent_type)
            
            async with self.session.post(
                f"{self.base_url}/api/v1/agents/upload-document",
                data=data
            ) as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def get_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """获取Agent状态"""
        try:
            async with self.session.get(f"{self.base_url}/api/v1/agents/{agent_id}/status") as response:
                return await response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}
    
    async def execute_complete_workflow(self, requirement_text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """执行完整工作流程"""
        workflow_results = {
            "requirement_analysis": None,
            "requirement_review": None,
            "testcase_generation": None,
            "testcase_review": None
        }
        
        try:
            # 1. 需求分析
            st.info("🔄 执行需求分析...")
            analysis_result = await self.analyze_requirements(requirement_text, context)
            workflow_results["requirement_analysis"] = analysis_result
            
            if analysis_result.get("status") == "error":
                return {"status": "error", "stage": "requirement_analysis", "results": workflow_results}
            
            # 2. 需求评审
            st.info("🔄 执行需求评审...")
            requirements = analysis_result.get("requirements", [])
            review_result = await self.review_requirements(requirements, context)
            workflow_results["requirement_review"] = review_result
            
            if review_result.get("status") == "error":
                return {"status": "error", "stage": "requirement_review", "results": workflow_results}
            
            # 3. 测试用例生成
            st.info("🔄 生成测试用例...")
            testcase_result = await self.generate_test_cases(requirements, context)
            workflow_results["testcase_generation"] = testcase_result
            
            if testcase_result.get("status") == "error":
                return {"status": "error", "stage": "testcase_generation", "results": workflow_results}
            
            # 4. 测试用例评审
            st.info("🔄 评审测试用例...")
            test_cases = testcase_result.get("test_cases", [])
            final_review = await self.review_test_cases(test_cases, context)
            workflow_results["testcase_review"] = final_review
            
            if final_review.get("status") == "error":
                return {"status": "error", "stage": "testcase_review", "results": workflow_results}
            
            return {"status": "success", "results": workflow_results}
            
        except Exception as e:
            return {"status": "error", "message": str(e), "results": workflow_results}


class StreamlitAPIIntegration:
    """Streamlit API集成类"""
    
    def __init__(self, api_base_url: str = "http://localhost:8000"):
        self.api_base_url = api_base_url
    
    async def run_workflow_with_progress(self, requirement_text: str, context: Dict[str, Any] = None):
        """带进度显示的工作流程执行"""
        
        # 创建进度条和状态显示
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        async with APIClient(self.api_base_url) as client:
            # 检查API连接
            status_text.text("🔍 检查API连接...")
            health = await client.health_check()
            
            if health.get("status") == "error":
                st.error(f"❌ API连接失败: {health.get('message')}")
                return None
            
            progress_bar.progress(10)
            
            # 执行完整工作流程
            status_text.text("🚀 开始执行工作流程...")
            result = await client.execute_complete_workflow(requirement_text, context)
            
            progress_bar.progress(100)
            status_text.text("✅ 工作流程完成")
            
            return result
    
    def display_api_result(self, result: Dict[str, Any]):
        """显示API结果"""
        if not result:
            return
        
        if result.get("status") == "error":
            st.error(f"❌ 执行失败: {result.get('message', '未知错误')}")
            if "stage" in result:
                st.error(f"失败阶段: {result['stage']}")
            return
        
        # 显示成功结果
        st.success("🎉 工作流程执行成功！")
        
        results = result.get("results", {})
        
        # 创建结果展示标签页
        tabs = st.tabs(["需求分析", "需求评审", "用例生成", "用例评审"])
        
        with tabs[0]:
            self._display_requirement_analysis(results.get("requirement_analysis"))
        
        with tabs[1]:
            self._display_requirement_review(results.get("requirement_review"))
        
        with tabs[2]:
            self._display_testcase_generation(results.get("testcase_generation"))
        
        with tabs[3]:
            self._display_testcase_review(results.get("testcase_review"))
    
    def _display_requirement_analysis(self, result: Dict[str, Any]):
        """显示需求分析结果"""
        if not result:
            st.warning("暂无需求分析结果")
            return
        
        st.subheader("📊 需求分析结果")
        
        # 显示原始结果（JSON格式）
        with st.expander("🔍 查看详细结果"):
            st.json(result)
        
        # 提取并显示关键信息
        if "requirements" in result:
            st.subheader("📋 识别的需求")
            for i, req in enumerate(result["requirements"], 1):
                st.write(f"{i}. {req}")
    
    def _display_requirement_review(self, result: Dict[str, Any]):
        """显示需求评审结果"""
        if not result:
            st.warning("暂无需求评审结果")
            return
        
        st.subheader("🔍 需求评审结果")
        
        with st.expander("🔍 查看详细结果"):
            st.json(result)
    
    def _display_testcase_generation(self, result: Dict[str, Any]):
        """显示测试用例生成结果"""
        if not result:
            st.warning("暂无测试用例生成结果")
            return
        
        st.subheader("📝 测试用例生成结果")
        
        with st.expander("🔍 查看详细结果"):
            st.json(result)
        
        # 显示测试用例
        if "test_cases" in result:
            st.subheader("📋 生成的测试用例")
            for i, tc in enumerate(result["test_cases"], 1):
                with st.expander(f"测试用例 {i}"):
                    st.write(tc)
    
    def _display_testcase_review(self, result: Dict[str, Any]):
        """显示测试用例评审结果"""
        if not result:
            st.warning("暂无测试用例评审结果")
            return
        
        st.subheader("✅ 测试用例评审结果")
        
        with st.expander("🔍 查看详细结果"):
            st.json(result)


# 工具函数
def run_async_function(coro):
    """在Streamlit中运行异步函数"""
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    
    return loop.run_until_complete(coro)
