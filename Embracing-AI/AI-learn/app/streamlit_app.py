#!/usr/bin/env python3
"""
智能测试用例生成系统 - Streamlit前端界面
展示从需求分析到用例评审的完整流程
"""

import streamlit as st
import asyncio
import json
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from scripts.start_system import SystemManager
from core.logger import get_agent_logger
from models.enums import AgentType, AgentStatus
from streamlit_api_client import StreamlitAPIIntegration, run_async_function

# 配置页面
st.set_page_config(
    page_title="智能测试用例生成系统",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化session state
if 'workflow_results' not in st.session_state:
    st.session_state.workflow_results = {}
if 'current_stage' not in st.session_state:
    st.session_state.current_stage = 0
if 'system_manager' not in st.session_state:
    st.session_state.system_manager = None
if 'processing' not in st.session_state:
    st.session_state.processing = False
if 'api_integration' not in st.session_state:
    st.session_state.api_integration = StreamlitAPIIntegration()
if 'use_real_api' not in st.session_state:
    st.session_state.use_real_api = False

# 预定义的示例需求
SAMPLE_REQUIREMENTS = {
    "用户登录": """
用户登录功能需求：
1. 用户可以通过用户名和密码登录系统
2. 登录成功后跳转到主页面
3. 登录失败时显示错误提示
4. 支持记住密码功能
5. 连续登录失败3次后锁定账户30分钟
""",
    "购物车": """
购物车功能需求：
1. 用户可以将商品添加到购物车
2. 用户可以修改购物车中商品的数量
3. 用户可以删除购物车中的商品
4. 购物车显示商品总价和数量
5. 购物车数据在用户会话期间保持
""",
    "文件上传": """
文件上传功能需求：
1. 支持上传图片、文档、视频等多种格式文件
2. 单个文件大小不超过100MB
3. 支持批量上传，最多10个文件
4. 上传过程显示进度条
5. 上传完成后返回文件访问链接
6. 支持拖拽上传
"""
}

def main():
    """主函数"""
    st.title("🤖 智能测试用例生成系统")
    st.markdown("---")
    
    # 侧边栏 - 系统状态和配置
    with st.sidebar:
        st.header("🔧 系统配置")

        # 运行模式选择
        st.session_state.use_real_api = st.checkbox(
            "🌐 使用真实API",
            value=st.session_state.use_real_api,
            help="勾选后将调用真实的后端API，否则使用模拟数据"
        )

        if st.session_state.use_real_api:
            # API配置
            api_url = st.text_input(
                "API地址",
                value="http://localhost:8000",
                help="后端API服务地址"
            )
            st.session_state.api_integration = StreamlitAPIIntegration(api_url)

            # API连接测试
            if st.button("🔍 测试API连接"):
                with st.spinner("测试连接中..."):
                    test_api_connection(api_url)
        else:
            # 系统初始化（模拟模式）
            if st.button("🚀 初始化系统", type="primary"):
                with st.spinner("正在初始化系统组件..."):
                    initialize_system()

            # 显示系统状态
            display_system_status()

        st.markdown("---")
        st.header("📋 预定义需求")
        
        # 预定义需求选择
        selected_sample = st.selectbox(
            "选择示例需求",
            ["自定义"] + list(SAMPLE_REQUIREMENTS.keys())
        )
        
        if selected_sample != "自定义":
            if st.button("📥 加载示例需求"):
                st.session_state.selected_requirement = SAMPLE_REQUIREMENTS[selected_sample]
                st.rerun()
    
    # 主界面
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📝 需求输入")
        
        # 需求输入方式选择
        input_method = st.radio(
            "输入方式",
            ["文本输入", "文档上传"]
        )
        
        requirement_text = ""
        
        if input_method == "文本输入":
            # 文本输入
            requirement_text = st.text_area(
                "请输入需求描述",
                value=st.session_state.get('selected_requirement', ''),
                height=300,
                placeholder="请详细描述功能需求..."
            )
        else:
            # 文档上传
            uploaded_file = st.file_uploader(
                "上传需求文档",
                type=['txt', 'md', 'docx'],
                help="支持 .txt, .md, .docx 格式"
            )
            
            if uploaded_file is not None:
                if uploaded_file.type == "text/plain":
                    requirement_text = str(uploaded_file.read(), "utf-8")
                elif uploaded_file.name.endswith('.md'):
                    requirement_text = str(uploaded_file.read(), "utf-8")
                else:
                    st.warning("Word文档将通过后端处理")
                    requirement_text = f"[上传的文档: {uploaded_file.name}]"
        
        # 开始处理按钮
        if st.button("🚀 开始生成测试用例", type="primary", disabled=st.session_state.processing):
            if requirement_text.strip():
                if st.session_state.use_real_api:
                    start_real_workflow(requirement_text)
                else:
                    start_workflow(requirement_text)
            else:
                st.error("请输入需求描述或上传文档")
    
    with col2:
        st.header("🔄 处理流程")
        
        # 流程进度条
        display_workflow_progress()
        
        # 流程结果展示
        display_workflow_results()

def initialize_system():
    """初始化系统"""
    try:
        # 创建系统管理器
        system_manager = SystemManager()
        
        # 异步初始化
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(system_manager.initialize_components())
        
        st.session_state.system_manager = system_manager
        st.success("✅ 系统初始化成功")
        
    except Exception as e:
        st.error(f"❌ 系统初始化失败: {str(e)}")

def display_system_status():
    """显示系统状态"""
    if st.session_state.system_manager:
        health_status = st.session_state.system_manager.check_system_health()
        
        if health_status == "healthy":
            st.success("🟢 系统状态: 健康")
        elif health_status == "degraded":
            st.warning("🟡 系统状态: 降级")
        else:
            st.error("🔴 系统状态: 不健康")
        
        # 显示组件状态
        if hasattr(st.session_state.system_manager, 'components_status'):
            with st.expander("📊 组件详情"):
                for component, status in st.session_state.system_manager.components_status.items():
                    if status.startswith("healthy"):
                        st.success(f"✅ {component}: {status}")
                    elif "error" in status:
                        st.error(f"❌ {component}: {status}")
                    else:
                        st.warning(f"⚠️ {component}: {status}")
    else:
        st.info("🔄 请先初始化系统")

def test_api_connection(api_url: str):
    """测试API连接"""
    try:
        import requests
        response = requests.get(f"{api_url}/api/v1/system/health", timeout=5)
        if response.status_code == 200:
            st.success("✅ API连接成功")
        else:
            st.error(f"❌ API连接失败: HTTP {response.status_code}")
    except Exception as e:
        st.error(f"❌ API连接失败: {str(e)}")

def start_real_workflow(requirement_text: str):
    """启动真实API工作流程"""
    st.session_state.processing = True
    st.session_state.workflow_results = {}

    try:
        # 使用真实API执行工作流程
        with st.spinner("🔄 正在执行工作流程..."):
            result = run_async_function(
                st.session_state.api_integration.run_workflow_with_progress(requirement_text)
            )

            if result:
                st.session_state.workflow_results = result.get("results", {})
                if result.get("status") == "success":
                    st.success("🎉 工作流程执行成功！")
                else:
                    st.error(f"❌ 工作流程执行失败: {result.get('message', '未知错误')}")
            else:
                st.error("❌ 无法获取工作流程结果")

    except Exception as e:
        st.error(f"❌ 工作流程执行异常: {str(e)}")
    finally:
        st.session_state.processing = False

def start_workflow(requirement_text: str):
    """开始工作流程"""
    st.session_state.processing = True
    st.session_state.current_stage = 0
    st.session_state.workflow_results = {}
    
    # 创建进度占位符
    progress_placeholder = st.empty()
    
    try:
        if not st.session_state.system_manager:
            st.error("请先初始化系统")
            return
        
        # 异步执行工作流程
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        # 模拟工作流程执行
        simulate_workflow_execution(requirement_text, progress_placeholder)
        
    except Exception as e:
        st.error(f"工作流程执行失败: {str(e)}")
    finally:
        st.session_state.processing = False

def simulate_workflow_execution(requirement_text: str, progress_placeholder):
    """模拟工作流程执行"""
    stages = [
        "需求分析",
        "需求评审", 
        "用例设计",
        "用例评审"
    ]
    
    for i, stage in enumerate(stages):
        st.session_state.current_stage = i
        
        with progress_placeholder.container():
            st.info(f"🔄 正在执行: {stage}")
            progress_bar = st.progress(0)
            
            # 模拟处理过程
            for j in range(100):
                time.sleep(0.02)  # 模拟处理时间
                progress_bar.progress(j + 1)
            
            # 生成模拟结果
            result = generate_mock_result(stage, requirement_text)
            st.session_state.workflow_results[stage] = result
            
            st.success(f"✅ {stage} 完成")
        
        time.sleep(0.5)  # 阶段间隔

def generate_mock_result(stage: str, requirement_text: str) -> Dict[str, Any]:
    """生成模拟结果"""
    base_result = {
        "stage": stage,
        "timestamp": datetime.now().isoformat(),
        "status": "completed",
        "input": requirement_text[:100] + "..." if len(requirement_text) > 100 else requirement_text
    }
    
    if stage == "需求分析":
        base_result.update({
            "functional_requirements": [
                {"id": "FR001", "description": "用户登录验证", "priority": "高"},
                {"id": "FR002", "description": "密码加密存储", "priority": "高"},
                {"id": "FR003", "description": "记住密码功能", "priority": "中"}
            ],
            "non_functional_requirements": [
                {"id": "NFR001", "description": "响应时间<2秒", "priority": "高"},
                {"id": "NFR002", "description": "支持1000并发用户", "priority": "中"}
            ],
            "acceptance_criteria": [
                "用户输入正确用户名密码后能成功登录",
                "用户输入错误密码时显示错误提示",
                "连续3次失败后锁定账户"
            ]
        })
    
    elif stage == "需求评审":
        base_result.update({
            "review_result": "通过",
            "completeness_score": 85,
            "clarity_score": 90,
            "testability_score": 88,
            "issues": [
                {"type": "建议", "description": "建议明确锁定时间的具体实现"},
                {"type": "澄清", "description": "需要确认密码复杂度要求"}
            ],
            "recommendations": [
                "添加密码强度验证需求",
                "明确用户角色和权限"
            ]
        })
    
    elif stage == "用例设计":
        base_result.update({
            "test_cases": [
                {
                    "id": "TC001",
                    "title": "正常登录测试",
                    "priority": "高",
                    "type": "功能测试",
                    "steps": ["输入正确用户名", "输入正确密码", "点击登录"],
                    "expected": "成功登录并跳转到主页"
                },
                {
                    "id": "TC002", 
                    "title": "错误密码测试",
                    "priority": "高",
                    "type": "负面测试",
                    "steps": ["输入正确用户名", "输入错误密码", "点击登录"],
                    "expected": "显示错误提示信息"
                },
                {
                    "id": "TC003",
                    "title": "账户锁定测试", 
                    "priority": "中",
                    "type": "安全测试",
                    "steps": ["连续3次输入错误密码"],
                    "expected": "账户被锁定30分钟"
                }
            ],
            "coverage_analysis": {
                "requirement_coverage": "95%",
                "scenario_coverage": "90%",
                "total_test_cases": 15
            }
        })
    
    elif stage == "用例评审":
        base_result.update({
            "review_result": "通过",
            "quality_score": 92,
            "coverage_score": 95,
            "executability_score": 88,
            "issues": [
                {"type": "优化", "description": "TC003可以增加更详细的验证步骤"}
            ],
            "final_recommendations": [
                "建议增加性能测试用例",
                "考虑添加兼容性测试场景"
            ],
            "approved_test_cases": 15,
            "total_test_cases": 15
        })
    
    return base_result

def display_workflow_progress():
    """显示工作流程进度"""
    stages = ["需求分析", "需求评审", "用例设计", "用例评审"]
    
    # 创建进度指示器
    cols = st.columns(4)
    
    for i, (col, stage) in enumerate(zip(cols, stages)):
        with col:
            if i < st.session_state.current_stage:
                st.success(f"✅ {stage}")
            elif i == st.session_state.current_stage and st.session_state.processing:
                st.info(f"🔄 {stage}")
            elif i == st.session_state.current_stage and not st.session_state.processing and stage in st.session_state.workflow_results:
                st.success(f"✅ {stage}")
            else:
                st.gray(f"⏳ {stage}")

def display_workflow_results():
    """显示工作流程结果"""
    if not st.session_state.workflow_results:
        st.info("🔄 等待处理结果...")
        return

    if st.session_state.use_real_api:
        # 显示真实API结果
        st.session_state.api_integration.display_api_result({
            "status": "success",
            "results": st.session_state.workflow_results
        })
    else:
        # 显示模拟结果
        # 创建标签页
        tabs = st.tabs(list(st.session_state.workflow_results.keys()))

        for tab, (stage, result) in zip(tabs, st.session_state.workflow_results.items()):
            with tab:
                display_stage_result(stage, result)

def display_stage_result(stage: str, result: Dict[str, Any]):
    """显示单个阶段的结果"""
    st.subheader(f"📊 {stage} 结果")
    
    # 基本信息
    col1, col2 = st.columns(2)
    with col1:
        st.metric("状态", result.get("status", "未知"))
    with col2:
        st.metric("完成时间", result.get("timestamp", "未知")[:19])
    
    # 根据阶段显示特定内容
    if stage == "需求分析":
        display_requirement_analysis_result(result)
    elif stage == "需求评审":
        display_requirement_review_result(result)
    elif stage == "用例设计":
        display_testcase_generation_result(result)
    elif stage == "用例评审":
        display_testcase_review_result(result)

def display_requirement_analysis_result(result: Dict[str, Any]):
    """显示需求分析结果"""
    # 功能需求
    if "functional_requirements" in result:
        st.subheader("🎯 功能需求")
        df_func = pd.DataFrame(result["functional_requirements"])
        st.dataframe(df_func, use_container_width=True)
    
    # 非功能需求
    if "non_functional_requirements" in result:
        st.subheader("⚡ 非功能需求")
        df_nonfunc = pd.DataFrame(result["non_functional_requirements"])
        st.dataframe(df_nonfunc, use_container_width=True)
    
    # 验收标准
    if "acceptance_criteria" in result:
        st.subheader("✅ 验收标准")
        for i, criteria in enumerate(result["acceptance_criteria"], 1):
            st.write(f"{i}. {criteria}")

def display_requirement_review_result(result: Dict[str, Any]):
    """显示需求评审结果"""
    # 评审分数
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("完整性", f"{result.get('completeness_score', 0)}%")
    with col2:
        st.metric("清晰度", f"{result.get('clarity_score', 0)}%")
    with col3:
        st.metric("可测试性", f"{result.get('testability_score', 0)}%")
    
    # 问题和建议
    if "issues" in result:
        st.subheader("⚠️ 发现的问题")
        for issue in result["issues"]:
            st.warning(f"**{issue['type']}**: {issue['description']}")
    
    if "recommendations" in result:
        st.subheader("💡 改进建议")
        for rec in result["recommendations"]:
            st.info(f"• {rec}")

def display_testcase_generation_result(result: Dict[str, Any]):
    """显示测试用例生成结果"""
    # 覆盖率分析
    if "coverage_analysis" in result:
        st.subheader("📈 覆盖率分析")
        coverage = result["coverage_analysis"]
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("需求覆盖率", coverage.get("requirement_coverage", "0%"))
        with col2:
            st.metric("场景覆盖率", coverage.get("scenario_coverage", "0%"))
        with col3:
            st.metric("测试用例总数", coverage.get("total_test_cases", 0))
    
    # 测试用例列表
    if "test_cases" in result:
        st.subheader("📝 生成的测试用例")
        df_testcases = pd.DataFrame(result["test_cases"])
        st.dataframe(df_testcases, use_container_width=True)
        
        # 详细查看
        if st.button("📋 查看详细测试用例"):
            for tc in result["test_cases"]:
                with st.expander(f"{tc['id']}: {tc['title']}"):
                    st.write(f"**优先级**: {tc['priority']}")
                    st.write(f"**类型**: {tc['type']}")
                    st.write("**测试步骤**:")
                    for i, step in enumerate(tc['steps'], 1):
                        st.write(f"{i}. {step}")
                    st.write(f"**预期结果**: {tc['expected']}")

def display_testcase_review_result(result: Dict[str, Any]):
    """显示测试用例评审结果"""
    # 评审分数
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("质量分数", f"{result.get('quality_score', 0)}%")
    with col2:
        st.metric("覆盖率分数", f"{result.get('coverage_score', 0)}%")
    with col3:
        st.metric("可执行性", f"{result.get('executability_score', 0)}%")
    
    # 最终统计
    col1, col2 = st.columns(2)
    with col1:
        st.metric("通过的测试用例", result.get("approved_test_cases", 0))
    with col2:
        st.metric("总测试用例", result.get("total_test_cases", 0))
    
    # 最终建议
    if "final_recommendations" in result:
        st.subheader("🎯 最终建议")
        for rec in result["final_recommendations"]:
            st.success(f"• {rec}")
    
    # 下载按钮
    if st.button("📥 下载测试用例报告", type="primary"):
        # 这里可以实现下载功能
        st.success("报告下载功能待实现")

if __name__ == "__main__":
    main()
