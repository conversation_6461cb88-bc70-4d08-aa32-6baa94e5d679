# 智能测试用例生成系统 - Streamlit前端

## 概述

这是一个基于Streamlit的Web前端界面，用于展示智能测试用例生成系统的完整工作流程。系统支持从需求输入到测试用例生成的全流程可视化展示。

## 功能特性

### 🎯 核心功能
- **需求输入**: 支持文本输入和文档上传两种方式
- **流程可视化**: 实时展示四个阶段的执行进度
- **结果展示**: 详细展示每个阶段的处理过程和结果
- **双模式运行**: 支持模拟模式和真实API模式

### 📊 四个处理阶段
1. **需求分析阶段** - 解析需求文档，提取功能点和验收标准
2. **需求评审阶段** - 评审需求完整性、一致性和可测试性
3. **用例设计阶段** - 基于多种测试设计方法生成测试用例
4. **用例评审阶段** - 评审测试用例质量和覆盖率

## 快速开始

### 1. 环境准备

确保已安装必要的依赖：

```bash
# 安装Streamlit
pip install streamlit

# 安装其他依赖
pip install aiohttp requests pandas
```

### 2. 启动应用

#### 方式一：使用启动脚本（推荐）
```bash
python run_streamlit.py
```

#### 方式二：直接使用Streamlit命令
```bash
streamlit run streamlit_app.py --server.port 8501
```

### 3. 访问界面

打开浏览器访问：http://localhost:8501

## 使用指南

### 🔧 系统配置

#### 模拟模式（默认）
- 适合演示和测试
- 使用预定义的模拟数据
- 无需后端API服务

#### 真实API模式
- 调用真实的后端API服务
- 需要先启动后端服务
- 支持自定义API地址

### 📝 需求输入

#### 文本输入
1. 选择"文本输入"方式
2. 在文本框中输入详细的需求描述
3. 可以使用预定义的示例需求

#### 文档上传
1. 选择"文档上传"方式
2. 上传支持的文档格式（.txt, .md, .docx）
3. 系统会自动解析文档内容

### 🔄 工作流程执行

1. **输入需求**: 选择输入方式并提供需求信息
2. **选择模式**: 在侧边栏选择运行模式
3. **开始处理**: 点击"开始生成测试用例"按钮
4. **查看进度**: 实时查看四个阶段的执行进度
5. **查看结果**: 在结果区域查看详细的处理结果

### 📊 结果展示

#### 需求分析结果
- 功能需求列表
- 非功能需求列表
- 验收标准
- 风险评估

#### 需求评审结果
- 完整性评分
- 清晰度评分
- 可测试性评分
- 发现的问题和改进建议

#### 用例设计结果
- 生成的测试用例列表
- 覆盖率分析
- 测试用例分类
- 优先级分配

#### 用例评审结果
- 质量评分
- 最终统计
- 评审建议
- 可下载报告

## 预定义示例

系统提供了三个预定义的示例需求：

### 1. 用户登录
- 用户名密码登录
- 记住密码功能
- 账户锁定机制

### 2. 购物车
- 商品添加/删除
- 数量修改
- 价格计算

### 3. 文件上传
- 多格式支持
- 批量上传
- 进度显示

## 技术架构

### 前端技术栈
- **Streamlit**: Web应用框架
- **Pandas**: 数据处理和展示
- **aiohttp**: 异步HTTP客户端
- **asyncio**: 异步编程支持

### 后端集成
- **FastAPI**: 后端API框架
- **多智能体系统**: 四个专业化Agent
- **向量记忆**: ChromaDB存储
- **可追溯性**: Redis管理

## 配置选项

### 系统配置
```python
# API地址配置
API_BASE_URL = "http://localhost:8000"

# Streamlit配置
STREAMLIT_PORT = 8501
STREAMLIT_HOST = "0.0.0.0"
```

### 页面配置
- 宽屏布局
- 侧边栏展开
- 浅色主题
- 自定义图标

## 故障排除

### 常见问题

#### 1. 无法启动Streamlit
```bash
# 检查Streamlit安装
pip install --upgrade streamlit

# 检查端口占用
lsof -i :8501
```

#### 2. API连接失败
- 确认后端服务已启动
- 检查API地址配置
- 验证网络连接

#### 3. 文档上传失败
- 检查文件格式是否支持
- 确认文件大小限制
- 验证文件编码

### 日志查看
```bash
# 查看Streamlit日志
streamlit run streamlit_app.py --logger.level debug
```

## 开发指南

### 添加新功能
1. 在`streamlit_app.py`中添加UI组件
2. 在`streamlit_api_client.py`中添加API调用
3. 更新结果显示函数

### 自定义样式
```python
# 添加自定义CSS
st.markdown("""
<style>
.custom-style {
    /* 自定义样式 */
}
</style>
""", unsafe_allow_html=True)
```

### 扩展API客户端
```python
# 在APIClient类中添加新方法
async def new_api_method(self, params):
    # 实现新的API调用
    pass
```

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
