#!/usr/bin/env python3
"""
启动Streamlit应用的脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """主函数"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    
    # 设置环境变量
    os.environ["PYTHONPATH"] = str(current_dir)
    
    # Streamlit应用文件路径
    app_file = current_dir / "streamlit_app.py"
    
    if not app_file.exists():
        print(f"❌ 找不到Streamlit应用文件: {app_file}")
        sys.exit(1)
    
    # 启动Streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(app_file),
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--theme.base", "light"
    ]
    
    print("🚀 启动智能测试用例生成系统...")
    print(f"📱 访问地址: http://localhost:8501")
    print("🔧 使用 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        subprocess.run(cmd, cwd=current_dir)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
