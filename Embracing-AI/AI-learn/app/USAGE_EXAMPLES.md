# 工作流程演示使用示例

## 🚀 快速开始

### 基本命令
```bash
cd /path/to/Embracing-AI/AI-learn/app
source ~/.bash_profile && conda activate autogen
python scripts/simple_workflow_demo.py --requirement "你的需求描述"
```

## 📝 支持的需求输入格式

演示脚本支持多种需求输入格式，会自动识别并解析：

### 1. 简单描述格式
```bash
python scripts/simple_workflow_demo.py --requirement "用户登录功能:填写用户名\密码,点击登录"
```

**输出示例：**
```
🔍 第一步：需求分析
============================================================
输入需求：用户登录功能:填写用户名\密码,点击登录
📝 正在分析需求...
✅ 需求分析完成，提取到 1 个需求
   - REQ_001: 用户登录功能:填写用户名\密码,点击登录...
```

### 2. 冒号分隔格式
```bash
python scripts/simple_workflow_demo.py --requirement "购物车功能：添加商品，修改数量，删除商品，计算总价"
```

### 3. 数字列表格式
```bash
python scripts/simple_workflow_demo.py --requirement "
用户注册功能：
1. 用户填写注册信息
2. 系统验证信息有效性
3. 发送验证邮件
4. 用户确认注册
"
```

### 4. 多行描述格式
```bash
python scripts/simple_workflow_demo.py --requirement "
文件上传功能
支持多种文件格式
显示上传进度
验证文件大小
"
```

## 🎯 完整演示流程

每次运行都会经历四个步骤：

### 第一步：需求分析 🔍
- 解析输入的需求文本
- 提取结构化需求信息
- 分配需求ID和优先级

### 第二步：需求评审 📋
- 评估需求质量
- 计算评审评分
- 识别潜在问题

### 第三步：测试用例生成 🧪
- 为每个需求生成测试用例
- 包含正向、负向、边界测试
- 计算覆盖率统计

### 第四步：测试用例评审 ✅
- 评审测试用例质量
- 计算最终评分
- 生成改进建议

## 📊 输出解读

### 评分系统
- **85-100分**：优秀，通过评审
- **70-84分**：良好，可能需要小幅修改
- **60-69分**：一般，需要改进
- **60分以下**：不合格，需要重新设计

### 状态说明
- **通过**：质量良好，可以进入下一阶段
- **有问题**：存在需要解决的问题
- **需要修改**：必须修改后才能继续

## 🔧 高级用法

### 1. 查看详细日志
```bash
# 设置日志级别为DEBUG
export LOG_LEVEL=DEBUG
python scripts/simple_workflow_demo.py --requirement "你的需求"
```

### 2. 使用预定义样例
```bash
# 查看所有预定义样例
python scripts/demo_workflow.py --list-samples

# 使用特定样例
python scripts/demo_workflow.py --sample 用户登录
python scripts/demo_workflow.py --sample 购物车
python scripts/demo_workflow.py --sample 文件上传
```

### 3. 通过系统启动脚本
```bash
# 运行完整工作流程后启动API服务
python scripts/start_system.py --requirement "你的需求"

# 跳过测试直接启动服务
python scripts/start_system.py --skip-tests
```

## 🎨 实际应用场景

### 场景1：敏捷开发需求分析
```bash
python scripts/simple_workflow_demo.py --requirement "
作为用户，我希望能够快速搜索商品
以便找到我需要的产品
接受标准：
- 支持关键词搜索
- 搜索结果按相关性排序
- 搜索响应时间小于2秒
"
```

### 场景2：API接口设计
```bash
python scripts/simple_workflow_demo.py --requirement "
用户认证API：
- POST /api/auth/login 用户登录
- POST /api/auth/logout 用户登出
- GET /api/auth/profile 获取用户信息
- PUT /api/auth/profile 更新用户信息
"
```

### 场景3：移动应用功能
```bash
python scripts/simple_workflow_demo.py --requirement "
消息推送功能：
支持文本、图片、链接推送
用户可以设置推送偏好
支持定时推送和即时推送
提供推送统计和分析
"
```

## 🐛 常见问题

### Q1: 提示"提取到0个需求"
**原因**：需求格式不被识别
**解决**：尝试使用冒号、逗号或数字列表格式

### Q2: 除零错误 (ZeroDivisionError)
**原因**：旧版本的bug，已修复
**解决**：更新到最新版本的演示脚本

### Q3: Ollama连接失败
**原因**：Ollama服务未启动
**解决**：
```bash
# 启动Ollama服务
ollama serve

# 检查可用模型
ollama list
```

### Q4: ChromaDB初始化失败
**原因**：数据库文件权限问题
**解决**：
```bash
# 清理数据重新初始化
rm -rf data/chroma/
```

## 📈 性能优化建议

### 1. 硬件要求
- **内存**：建议8GB以上
- **CPU**：建议4核以上
- **存储**：SSD硬盘，至少10GB可用空间

### 2. 软件优化
- 使用本地LLM模型减少网络延迟
- 定期清理向量数据库
- 调整批处理大小

### 3. 网络优化
- 确保稳定的网络连接
- 使用本地部署减少外部依赖

## 🔗 相关文档

- [系统架构说明](README_WORKFLOW_DEMO.md)
- [API文档](api/README.md)
- [开发指南](DEVELOPMENT.md)
- [故障排除](TROUBLESHOOTING.md)

---

**提示**：如果遇到问题，请查看日志文件 `logs/` 目录下的详细错误信息。
