#!/usr/bin/env python3
"""
Streamlit应用演示脚本
展示如何使用智能测试用例生成系统的Web界面
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                智能测试用例生成系统                          ║
║                  Streamlit Web界面                           ║
╠══════════════════════════════════════════════════════════════╣
║  🤖 AI驱动的测试用例自动生成                                ║
║  📊 可视化的工作流程展示                                    ║
║  🔄 四阶段智能协作处理                                      ║
║  📝 支持需求文档和文本输入                                  ║
╚══════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        "streamlit",
        "pandas", 
        "aiohttp",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖已安装")
    return True

def start_backend_api():
    """启动后端API（可选）"""
    print("\n🔧 后端API配置")
    print("如果要使用真实API模式，请确保后端服务已启动:")
    print("  1. 在另一个终端运行: python scripts/start_system.py")
    print("  2. 或者使用模拟模式进行演示")
    
    choice = input("\n是否现在启动后端API? (y/N): ").lower().strip()
    
    if choice == 'y':
        try:
            print("🚀 启动后端API服务...")
            # 这里可以添加启动后端的逻辑
            print("💡 请在另一个终端手动启动后端服务")
            input("按Enter键继续...")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            print("💡 可以稍后在Streamlit界面中使用模拟模式")

def start_streamlit():
    """启动Streamlit应用"""
    print("\n🚀 启动Streamlit应用...")
    
    current_dir = Path(__file__).parent
    app_file = current_dir / "streamlit_app.py"
    
    if not app_file.exists():
        print(f"❌ 找不到应用文件: {app_file}")
        return False
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(app_file),
        "--server.port", "8501",
        "--server.address", "0.0.0.0",
        "--theme.base", "light",
        "--server.headless", "false"
    ]
    
    print("📱 应用将在以下地址启动:")
    print("   本地访问: http://localhost:8501")
    print("   网络访问: http://0.0.0.0:8501")
    print("\n🔧 使用说明:")
    print("   1. 在侧边栏选择运行模式（模拟/真实API）")
    print("   2. 输入需求或选择预定义示例")
    print("   3. 点击'开始生成测试用例'按钮")
    print("   4. 查看四个阶段的处理结果")
    print("\n⏹️  使用 Ctrl+C 停止服务")
    print("-" * 60)
    
    try:
        # 延迟打开浏览器
        def open_browser():
            time.sleep(3)
            webbrowser.open("http://localhost:8501")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Streamlit
        subprocess.run(cmd, cwd=current_dir)
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例:")
    print("\n1️⃣  模拟模式演示:")
    print("   - 选择预定义需求（如'用户登录'）")
    print("   - 点击'开始生成测试用例'")
    print("   - 观察四个阶段的执行过程")
    print("   - 查看详细的分析和生成结果")
    
    print("\n2️⃣  真实API模式:")
    print("   - 确保后端服务已启动")
    print("   - 勾选'使用真实API'")
    print("   - 测试API连接")
    print("   - 输入自定义需求进行处理")
    
    print("\n3️⃣  文档上传:")
    print("   - 选择'文档上传'方式")
    print("   - 上传.txt、.md或.docx文件")
    print("   - 系统自动解析文档内容")
    
    print("\n📊 结果查看:")
    print("   - 需求分析: 功能需求、验收标准")
    print("   - 需求评审: 完整性、清晰度评分")
    print("   - 用例设计: 测试用例、覆盖率分析")
    print("   - 用例评审: 质量评分、最终建议")

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 显示使用示例
    show_usage_examples()
    
    # 询问是否启动后端
    start_backend_api()
    
    # 启动Streamlit应用
    print("\n" + "="*60)
    if start_streamlit():
        print("✅ 演示完成")
    else:
        print("❌ 演示失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
